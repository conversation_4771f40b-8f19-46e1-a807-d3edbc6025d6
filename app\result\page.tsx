"use client";

import { useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback, useRef, Suspense } from 'react';
import Link from 'next/link';
import { RiasecScores, OceanScores } from '@/lib/types';
import { riasecDescriptions } from '@/lib/riasecQuestions';
import { getScoreLevel } from '@/lib/profileStore';
import { resultsApi } from '@/lib/client/apiService';
import { ResultsResponse } from '@/lib/api-types';
import RadarChart from '@/components/RadarChart';
import OceanChart from '@/components/OceanChart';
import ProfileSummary from '@/components/ProfileSummary';

function ResultContent() {
  const searchParams = useSearchParams();
  // State untuk data gabungan RIASEC + OCEAN
  const [scores, setScores] = useState<RiasecScores | null>(null);
  const [oceanScores, setOceanScores] = useState<OceanScores | null>(null);
  const [resultsData, setResultsData] = useState<ResultsResponse | null>(null);
  // State umum
  const [error, setError] = useState<string | null>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState<boolean>(false);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const isGeneratingRef = useRef<boolean>(false);

  // Fungsi untuk generate combined profile interpretation
  const generateCombinedProfileInterpretation = useCallback(async (
    riasecScoresData: RiasecScores,
    oceanScoresData: OceanScores
  ) => {
    // Check if already generating using ref (prevents React Strict Mode double calls)
    if (isGeneratingRef.current || isGenerating) {
      console.log('Combined profile generation already in progress, skipping...');
      return;
    }

    // Additional check to prevent duplicate calls in development mode
    if (resultsData) {
      console.log('Results already exist, skipping duplicate generation...');
      return;
    }

    console.log('Starting combined profile generation for scores:', { riasecScoresData, oceanScoresData });
    isGeneratingRef.current = true;
    setIsGenerating(true);
    setIsLoadingProfile(true);
    try {
      const results = await resultsApi.get(riasecScoresData, oceanScoresData);
      setResultsData(results);
      console.log('Combined profile generation completed successfully');
    } catch (error) {
      console.error('Error generating combined profile interpretation:', error);
      setError('Gagal menghasilkan interpretasi profil gabungan. Silakan coba lagi.');
    } finally {
      isGeneratingRef.current = false;
      setIsLoadingProfile(false);
      setIsGenerating(false);
    }
  }, [isGenerating, resultsData]);

  useEffect(() => {
    // Reset states saat searchParams berubah
    setError(null);
    setResultsData(null);
    setIsGenerating(false);
    isGeneratingRef.current = false;
    try {
      // Parse RIASEC scores
      const r = parseInt(searchParams.get('r') || '0');
      const i = parseInt(searchParams.get('i') || '0');
      const a = parseInt(searchParams.get('a') || '0');
      const s = parseInt(searchParams.get('s') || '0');
      const e = parseInt(searchParams.get('e') || '0');
      const c = parseInt(searchParams.get('c') || '0');

      // Parse OCEAN scores (required)
      const o = parseInt(searchParams.get('o') || '0');
      const ocean_c = parseInt(searchParams.get('ocean_c') || '0');
      const ocean_e = parseInt(searchParams.get('ocean_e') || '0');
      const ocean_a = parseInt(searchParams.get('ocean_a') || '0');
      const n = parseInt(searchParams.get('n') || '0');

      // Validate RIASEC scores (each should be between 0-30 for 6 questions with 1-5 scale)
      if ([r, i, a, s, e, c].some(score => score < 0 || score > 30 || isNaN(score))) {
        setError('Data hasil tes RIASEC tidak valid. Silakan ulangi tes.');
        return;
      }

      // Validate OCEAN scores (each should be between 5-25 for 5 questions with 1-5 scale)
      if ([o, ocean_c, ocean_e, ocean_a, n].some(score => score < 5 || score > 25 || isNaN(score))) {
        setError('Data hasil tes OCEAN tidak valid. Silakan ulangi tes.');
        return;
      }

      const riasecScoresData = { R: r, I: i, A: a, S: s, E: e, C: c };
      const oceanScoresData = { O: o, C: ocean_c, E: ocean_e, A: ocean_a, N: n };

      // Set both scores
      setScores(riasecScoresData);
      setOceanScores(oceanScoresData);

      // Generate combined profile
      generateCombinedProfileInterpretation(riasecScoresData, oceanScoresData);
    } catch {
      setError('Terjadi kesalahan dalam memproses hasil tes.');
    }
  }, [searchParams]);

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center p-4">
        <div className="max-w-md mx-auto text-center bg-white rounded-2xl shadow-xl p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Error</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link 
            href="/"
            className="inline-block bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
          >
            Kembali ke Beranda
          </Link>
        </div>
      </div>
    );
  }

  if (!scores) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memproses hasil tes...</p>
        </div>
      </div>
    );
  }

  // Find the highest scoring type(s)
  const maxScore = Math.max(...Object.values(scores));
  const dominantTypes = Object.entries(scores)
    .filter(([, score]) => score === maxScore)
    .map(([type]) => type);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            Hasil Analisis Kepribadian & Minat Karir
          </h1>
          <p className="text-lg text-gray-600">
            Berikut adalah profil lengkap Anda berdasarkan analisis RIASEC dan Big Five (OCEAN)
          </p>
        </div>

        {/* Profile Summary Section - Moved to top */}
        {isLoadingProfile ? (
          <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl shadow-xl p-8 mb-8 text-white">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <h2 className="text-2xl font-semibold mb-2">Menganalisis Profil Anda...</h2>
              <p className="text-indigo-100">
                AI sedang menganalisis skor RIASEC dan OCEAN Anda untuk memberikan interpretasi yang komprehensif.
              </p>
            </div>
          </div>
        ) : resultsData ? (
          <ProfileSummary
            combinedProfile={{
              riasecData: {
                scores: resultsData.assessment.riasecScores,
                dominantTypes: Object.entries(resultsData.assessment.riasecScores)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 3)
                  .map(([type]) => type as any),
                level: getScoreLevel(Math.max(...Object.values(resultsData.assessment.riasecScores))).level
              },
              oceanData: {
                scores: resultsData.assessment.oceanScores,
                traits: Object.entries(resultsData.assessment.oceanScores).map(([trait, score]) => ({
                  trait: trait as any,
                  name: trait === 'O' ? 'Openness' : trait === 'C' ? 'Conscientiousness' : trait === 'E' ? 'Extraversion' : trait === 'A' ? 'Agreeableness' : 'Neuroticism',
                  score,
                  level: (score > 20 ? 'Tinggi' : score > 15 ? 'Sedang' : 'Rendah') as 'Rendah' | 'Sedang' | 'Tinggi',
                  description: `${trait} level ${score > 20 ? 'tinggi' : score > 15 ? 'sedang' : 'rendah'}`
                })),
                personalityType: 'Balanced'
              },
              profileTitle: resultsData.profile.profileTitle,
              profileDescription: resultsData.profile.profileDescription,
              strengths: resultsData.profile.strengths,
              careerSuggestions: resultsData.profile.careerSuggestions,
              workEnvironment: resultsData.profile.workEnvironment,
              developmentAreas: resultsData.profile.developmentAreas,
              personalityInsights: resultsData.profile.personalityInsights,
              careerFit: resultsData.profile.careerFit
            }}
            isCombinedMode={true}
          />
        ) : null}

        {/* RIASEC Chart Section */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
            Profil Minat Karir (RIASEC)
          </h2>
          <RadarChart scores={scores} />
        </div>

        {/* Interest Levels Summary */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">
            Ringkasan Minat Karir (RIASEC)
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(scores).map(([type, score]) => {
              const description = riasecDescriptions.find(d => d.type === type);
              const isHighest = dominantTypes.includes(type);
              const scoreLevel = getScoreLevel(score);
              return (
                <div
                  key={type}
                  className={`p-6 rounded-xl border-2 transition-all hover:shadow-lg ${
                    isHighest
                      ? 'border-indigo-500 bg-indigo-50 shadow-md'
                      : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center mb-4">
                    <div className={`text-2xl font-bold mb-3 ${
                      isHighest ? 'text-indigo-600' : 'text-gray-700'
                    }`}>
                      {scoreLevel.level === 'Tinggi' ? '🔥' : scoreLevel.level === 'Sedang' ? '⚡' : '💤'}
                    </div>
                    <div className={`text-sm font-semibold mb-2 ${
                      isHighest ? 'text-indigo-600' : 'text-gray-600'
                    }`}>
                      {description?.name} ({type})
                    </div>
                    <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium border ${scoreLevel.color}`}>
                      Minat {scoreLevel.level}
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 text-center">
                    {scoreLevel.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* RIASEC Descriptions */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">
            Penjelasan Tipe RIASEC
          </h2>
          <div className="grid gap-6">
            {riasecDescriptions.map((desc) => {
              const score = scores[desc.type];
              const isHighest = dominantTypes.includes(desc.type);
              const scoreLevel = getScoreLevel(score);
              return (
                <div
                  key={desc.type}
                  className={`p-6 rounded-lg border-l-4 transition-all hover:shadow-md ${
                    isHighest
                      ? 'border-indigo-500 bg-indigo-50'
                      : 'border-gray-300 bg-gray-50'
                  }`}
                >
                  <div className="flex justify-between items-start mb-3">
                    <h3 className={`text-lg font-semibold ${
                      isHighest ? 'text-indigo-700' : 'text-gray-700'
                    }`}>
                      {desc.name} ({desc.type})
                    </h3>
                    <div className="flex flex-col items-end space-y-1">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        isHighest
                          ? 'bg-indigo-200 text-indigo-800'
                          : 'bg-gray-200 text-gray-700'
                      }`}>
                        {scoreLevel.level === 'Tinggi' ? '🔥' : scoreLevel.level === 'Sedang' ? '⚡' : '💤'} Minat {scoreLevel.level}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    {desc.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* OCEAN Chart Section - Moved to bottom */}
        {oceanScores && <OceanChart scores={oceanScores} />}

        {/* Actions */}
        <div className="text-center">
          <div className="space-x-4">
            <Link 
              href="/"
              className="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
            >
              Kembali ke Beranda
            </Link>
            <Link 
              href="/test"
              className="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
            >
              Ulangi Tes
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ResultPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat hasil tes...</p>
        </div>
      </div>
    }>
      <ResultContent />
    </Suspense>
  );
}