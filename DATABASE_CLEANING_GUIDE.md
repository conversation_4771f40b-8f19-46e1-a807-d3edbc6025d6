# Database Cleaning Guide

## 🧹 Cara Membersihkan Database

Berikut adalah berbagai cara untuk membersihkan database sesuai kebutuhan Anda:

## 📋 <PERSON>kasan Perintah

| Perintah | Deskripsi | Tingkat Bahaya |
|----------|-----------|----------------|
| `npm run db:quick-clean` | Hapus semua assessment & profile (cepat) | ⚠️ Sedang |
| `npm run db:clean` | Tool interaktif dengan pilihan | ⚠️ Sedang |
| `npm run db:reset` | Reset database lengkap | 🚨 TINGGI |
| `npx prisma studio` | Lihat & edit data manual | ✅ Aman |

## 🚀 Metode Pembersihan

### 1. **Quick Clean (Direkomendasikan)**
```bash
npm run db:quick-clean
```
**Apa yang dihapus:**
- ✅ Semua assessment data
- ✅ Semua profile data
- ❌ User data (tetap ada)

**Kapan digunakan:**
- Ingin membersihkan data test
- <PERSON><PERSON><PERSON> fresh tanpa kehilangan user
- Pembersihan rutin

### 2. **Interactive Clean Tool**
```bash
npm run db:clean
```
**Fitur:**
- 📊 Menampilkan statistik database
- 🎯 Pilihan pembersihan selektif
- ⚠️ Konfirmasi sebelum menghapus
- 📅 Hapus data lama (berdasarkan tanggal)
- 🔍 Hapus data orphaned

**Pilihan yang tersedia:**
1. Clean everything (BAHAYA: Semua data hilang)
2. Clean only profiles
3. Clean only assessments (+ profiles)
4. Clean only users
5. Clean old assessments (> 30 hari)
6. Clean orphaned profiles
7. Show stats only

### 3. **Complete Reset (BAHAYA)**
```bash
npm run db:reset
```
⚠️ **PERINGATAN**: Menghapus **SEMUA DATA** dan membuat ulang tabel kosong!

**Kapan digunakan:**
- Development awal
- Perubahan schema besar
- Ketika database corrupt

### 4. **Manual Cleaning dengan Prisma Studio**
```bash
npx prisma studio
```
- Buka http://localhost:5555
- Edit/hapus data secara manual
- Aman untuk eksplorasi data

## 📊 Melihat Status Database

### Cek Statistik Database
```bash
npm run db:clean
# Pilih option 7 (Show stats only)
```

### Atau gunakan Prisma Studio
```bash
npx prisma studio
```

## 🛡️ Backup Sebelum Cleaning

### Backup Otomatis
Script cleaning otomatis membuat backup di:
```
scripts/backup-profiles.json
```

### Backup Manual
```bash
# Export semua data
npx prisma studio
# Atau gunakan script custom untuk export
```

## 📝 Contoh Penggunaan

### Scenario 1: Pembersihan Rutin
```bash
# Lihat status dulu
npm run db:clean
# Pilih option 7

# Bersihkan data test
npm run db:quick-clean
```

### Scenario 2: Pembersihan Selektif
```bash
npm run db:clean
# Pilih option yang sesuai:
# - Option 5: Hapus data lama
# - Option 6: Hapus orphaned data
```

### Scenario 3: Reset Lengkap (Development)
```bash
npm run db:reset
```

## 🔧 Script yang Tersedia

### 1. `scripts/clean-database.js`
- Tool interaktif lengkap
- Multiple cleaning options
- Safety confirmations
- Statistics display

### 2. `scripts/quick-clean.js`
- Pembersihan cepat
- Hapus assessment & profile
- Preserve user data

### 3. NPM Scripts Baru
```json
{
  "db:clean": "node scripts/clean-database.js",
  "db:quick-clean": "node scripts/quick-clean.js",
  "db:reset": "prisma db push --force-reset"
}
```

## ⚠️ Peringatan Penting

1. **Selalu backup data penting** sebelum cleaning
2. **Jangan jalankan di production** tanpa backup
3. **db:reset menghapus SEMUA DATA** - gunakan dengan hati-hati
4. **Foreign key constraints** - profiles akan terhapus otomatis jika assessment dihapus

## 🔄 Recovery

### Jika Data Terhapus Tidak Sengaja
1. Cek backup di `scripts/backup-profiles.json`
2. Gunakan Prisma Studio untuk restore manual
3. Atau jalankan script restore custom

### Restore dari Backup
```bash
# Manual restore menggunakan backup file
# (Script restore bisa dibuat jika diperlukan)
```

## 📈 Monitoring Database

### Cek Ukuran Database
```bash
# Untuk SQLite
ls -lh dev.db

# Atau lihat di Prisma Studio
npx prisma studio
```

### Regular Maintenance
- Jalankan `npm run db:clean` option 6 secara berkala (orphaned data)
- Hapus data lama dengan option 5
- Monitor pertumbuhan database

## 🎯 Best Practices

1. **Development**: Gunakan `npm run db:quick-clean` untuk testing
2. **Staging**: Gunakan interactive tool dengan konfirmasi
3. **Production**: Hanya gunakan selective cleaning dengan backup
4. **Regular**: Bersihkan data lama secara berkala

---

**💡 Tip**: Mulai dengan `npm run db:clean` option 7 untuk melihat status database sebelum melakukan pembersihan apapun.
