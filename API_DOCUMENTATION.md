# API Documentation

## Overview
This API provides endpoints for psychological assessment and career profiling using RIASEC and OCEAN personality models with AI-powered analysis.

## Base URL
- Development: `http://localhost:3000/api`
- Production: `https://your-domain.com/api`

## Authentication
Currently, no authentication is required. Future versions may include user authentication.

## Rate Limiting
- 100 requests per 15 minutes per IP address
- Rate limit headers are included in responses

## Error Handling
All endpoints return standardized error responses:

```json
{
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "statusCode": 400,
  "timestamp": "2025-07-13T08:53:17.616Z"
}
```

## Success Response Format
All successful responses follow this format:

```json
{
  "success": true,
  "data": { /* response data */ },
  "timestamp": "2025-07-13T08:53:17.616Z"
}
```

## Endpoints

### Health Check
**GET** `/health`

Check API and service health status.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "ok",
    "message": "API is running",
    "timestamp": "2025-07-13T08:53:17.616Z",
    "services": {
      "database": true,
      "gemini": true,
      "timestamp": "2025-07-13T08:53:17.455Z"
    }
  },
  "timestamp": "2025-07-13T08:53:17.616Z"
}
```

### Create Assessment
**POST** `/assessments`

Create a new psychological assessment with RIASEC and OCEAN scores.

**Request Body:**
```json
{
  "riasecScores": {
    "R": 15,  // Realistic (0-30)
    "I": 20,  // Investigative (0-30)
    "A": 12,  // Artistic (0-30)
    "S": 18,  // Social (0-30)
    "E": 16,  // Enterprising (0-30)
    "C": 14   // Conventional (0-30)
  },
  "oceanScores": {
    "O": 18,  // Openness (5-25)
    "C": 16,  // Conscientiousness (5-25)
    "E": 20,  // Extraversion (5-25)
    "A": 17,  // Agreeableness (5-25)
    "N": 12   // Neuroticism (5-25)
  },
  "userId": "optional-user-id"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cmd1fuc4d0000cuf09rcvab5t",
    "riasecScores": { /* scores */ },
    "oceanScores": { /* scores */ },
    "createdAt": "2025-07-13T08:54:16.707Z",
    "profileGenerated": false
  },
  "timestamp": "2025-07-13T08:54:16.707Z"
}
```

### Get Assessment
**GET** `/assessments?id={assessmentId}`

Retrieve an existing assessment by ID.

**Parameters:**
- `id` (required): Assessment ID

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cmd1fuc4d0000cuf09rcvab5t",
    "riasecScores": { /* scores */ },
    "oceanScores": { /* scores */ },
    "createdAt": "2025-07-13T08:54:16.707Z",
    "profileGenerated": true
  },
  "timestamp": "2025-07-13T08:54:16.707Z"
}
```

### Generate Profile
**POST** `/profiles/generate`

Generate AI-powered career profile from assessment scores.

**Request Body:**
```json
{
  "riasecScores": { /* RIASEC scores */ },
  "oceanScores": { /* OCEAN scores */ }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "profileTitle": "Analytical Problem Solver",
    "profileDescription": "Profile description...",
    "strengths": ["Strength 1", "Strength 2", ...],
    "careerSuggestions": ["Career 1", "Career 2", ...],
    "workEnvironment": "Work environment description...",
    "developmentAreas": ["Area 1", "Area 2", ...],
    "personalityInsights": ["Insight 1", "Insight 2", ...],
    "careerFit": "Career fit explanation...",
    "generatedAt": "2025-07-13T08:54:49.943Z"
  },
  "timestamp": "2025-07-13T08:54:49.943Z"
}
```

### Get Results
**GET** `/results?r={R}&i={I}&a={A}&s={S}&e={E}&c={C}&o={O}&ocean_c={C}&ocean_e={E}&ocean_a={A}&n={N}`

Get complete assessment results with AI-generated profile.

**Parameters:**
- RIASEC scores: `r`, `i`, `a`, `s`, `e`, `c` (0-30 each)
- OCEAN scores: `o`, `ocean_c`, `ocean_e`, `ocean_a`, `n` (5-25 each)

**Response:**
```json
{
  "success": true,
  "data": {
    "assessment": {
      "id": "cmd1fuc4d0000cuf09rcvab5t",
      "riasecScores": { /* scores */ },
      "oceanScores": { /* scores */ },
      "createdAt": "2025-07-13T08:54:49.923Z",
      "profileGenerated": true
    },
    "profile": {
      "profileTitle": "Analytical Problem Solver",
      "profileDescription": "Profile description...",
      "strengths": ["Strength 1", "Strength 2", ...],
      "careerSuggestions": ["Career 1", "Career 2", ...],
      "workEnvironment": "Work environment description...",
      "developmentAreas": ["Area 1", "Area 2", ...],
      "personalityInsights": ["Insight 1", "Insight 2", ...],
      "careerFit": "Career fit explanation...",
      "generatedAt": "2025-07-13T08:54:49.943Z"
    }
  },
  "timestamp": "2025-07-13T08:54:49.943Z"
}
```

## Error Codes

- `VALIDATION_ERROR` (400): Invalid input data
- `NOT_FOUND` (404): Resource not found
- `RATE_LIMIT_EXCEEDED` (429): Too many requests
- `CONFIGURATION_ERROR` (500): Server configuration issue
- `EXTERNAL_SERVICE_ERROR` (429): External service rate limit
- `TIMEOUT_ERROR` (408): Request timeout
- `DATABASE_ERROR` (500): Database operation failed
- `INTERNAL_ERROR` (500): Unexpected server error

## Performance Notes

- Profile generation typically takes 10-30 seconds
- Results are cached in database for faster subsequent access
- Rate limiting prevents abuse and ensures fair usage
- Timeout protection prevents hanging requests

## Data Storage

- All assessments are stored in SQLite database (development)
- Profiles are generated once and cached
- No personal data is stored without explicit user consent
- Data retention follows privacy guidelines
