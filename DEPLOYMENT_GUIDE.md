# Deployment Guide

## Overview
This guide covers deploying the AI-Driven Talent Mapping Assessment application with both frontend and backend components.

## Architecture
- **Frontend**: Next.js with App Router, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes with Prisma ORM
- **Database**: SQLite (development) / PostgreSQL (production)
- **AI Service**: Google Gemini AI for profile generation

## Environment Variables

### Required Environment Variables
```bash
# Gemini AI API Key (server-side)
GEMINI_API_KEY=your_gemini_api_key_here

# Database URL
DATABASE_URL="file:./dev.db"  # SQLite for development
# DATABASE_URL="postgresql://user:password@host:port/database"  # PostgreSQL for production

# Next.js Environment
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Optional Environment Variables
```bash
# For production
NODE_ENV=production

# For custom database settings
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000
```

## Development Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git

### Installation Steps
1. Clone the repository
```bash
git clone <repository-url>
cd atma-next
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
```bash
cp .env.local.example .env.local
# Edit .env.local with your API keys
```

4. Set up database
```bash
npx prisma generate
npx prisma db push
```

5. Start development server
```bash
npm run dev
```

## Production Deployment

### Option 1: Vercel (Recommended)

1. **Prepare for deployment**
```bash
npm run build
```

2. **Deploy to Vercel**
```bash
npx vercel
```

3. **Set environment variables in Vercel dashboard**
- `GEMINI_API_KEY`
- `DATABASE_URL` (use PostgreSQL for production)

4. **Configure database for production**
```bash
# Update DATABASE_URL to PostgreSQL
DATABASE_URL="postgresql://user:password@host:port/database"

# Run migrations
npx prisma db push
```

### Option 2: Docker Deployment

1. **Create Dockerfile**
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npx prisma generate
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

2. **Build and run**
```bash
docker build -t talent-mapping-app .
docker run -p 3000:3000 -e GEMINI_API_KEY=your_key talent-mapping-app
```

### Option 3: Traditional Server

1. **Build the application**
```bash
npm run build
```

2. **Set up process manager (PM2)**
```bash
npm install -g pm2
pm2 start npm --name "talent-mapping" -- start
```

3. **Configure reverse proxy (Nginx)**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Database Migration

### From SQLite to PostgreSQL

1. **Set up PostgreSQL database**
```sql
CREATE DATABASE talent_mapping;
CREATE USER talent_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE talent_mapping TO talent_user;
```

2. **Update environment variables**
```bash
DATABASE_URL="postgresql://talent_user:secure_password@localhost:5432/talent_mapping"
```

3. **Run migrations**
```bash
npx prisma db push
```

4. **Migrate data (if needed)**
```bash
# Export from SQLite
npx prisma db seed

# Import to PostgreSQL
npx prisma db push --force-reset
```

## Performance Optimization

### Frontend Optimization
- Enable Next.js image optimization
- Implement proper caching headers
- Use CDN for static assets
- Minimize bundle size

### Backend Optimization
- Implement database connection pooling
- Add Redis for caching
- Optimize Prisma queries
- Implement proper error logging

### AI Service Optimization
- Cache AI responses in database
- Implement request queuing
- Add fallback responses
- Monitor API usage

## Monitoring and Logging

### Application Monitoring
```bash
# Install monitoring tools
npm install @vercel/analytics
npm install @sentry/nextjs
```

### Database Monitoring
- Monitor connection pool usage
- Track query performance
- Set up automated backups

### API Monitoring
- Monitor response times
- Track error rates
- Monitor Gemini AI usage

## Security Considerations

### API Security
- Implement rate limiting (already included)
- Add CORS configuration
- Validate all inputs
- Use HTTPS in production

### Database Security
- Use connection pooling
- Implement proper access controls
- Regular security updates
- Encrypted connections

### Environment Security
- Never commit API keys
- Use secure environment variable management
- Implement proper secret rotation

## Backup and Recovery

### Database Backup
```bash
# PostgreSQL backup
pg_dump talent_mapping > backup.sql

# Restore
psql talent_mapping < backup.sql
```

### Application Backup
- Regular code repository backups
- Environment configuration backups
- Database schema versioning

## Troubleshooting

### Common Issues

1. **Gemini API errors**
   - Check API key validity
   - Monitor rate limits
   - Implement fallback responses

2. **Database connection issues**
   - Check connection string
   - Verify database server status
   - Monitor connection pool

3. **Build failures**
   - Clear Next.js cache: `rm -rf .next`
   - Regenerate Prisma client: `npx prisma generate`
   - Check TypeScript errors

### Performance Issues
- Monitor memory usage
- Check database query performance
- Optimize API response times
- Review error logs

## Maintenance

### Regular Tasks
- Update dependencies monthly
- Monitor API usage and costs
- Review and rotate API keys
- Database maintenance and optimization
- Security updates and patches

### Scaling Considerations
- Implement horizontal scaling
- Add load balancing
- Consider microservices architecture
- Implement caching layers
